using Godot;

public partial class Furnace3Menu : CanvasLayer
{
	private AnimationPlayer _animationPlayer;
	private Button _closeButton;
	private Button _mithrilBarSelectButton;
	private Button _erithrydiumBarSelectButton;

	// InfoBoard controls
	private Sprite2D _itemFront;
	private Label _amountToProduce;
	private Button _buttonMinusOne;
	private Button _buttonPlusOne;
	private Button _buttonSetOne;
	private Button _buttonSet25Percent;
	private Button _buttonSet50Percent;
	private Button _buttonSetMax;
	private Button _buttonProduce;

	private Furnace3 _currentFurnace;
	private ResourceType _selectedResource = ResourceType.None;
	private int _selectedAmount = 1;

	public override void _Ready()
	{
		_animationPlayer = GetNode<AnimationPlayer>("AnimationPlayer");
		_closeButton = GetNode<Button>("Control/Panel/CloseButton");
		_mithrilBarSelectButton = GetNode<Button>("Control/Panel/ScrollContainer/VBoxContainer/ItemListCopperBar/Button");
		_erithrydiumBarSelectButton = GetNode<Button>("Control/Panel/ScrollContainer/VBoxContainer/ItemListIronBar/Button");

		// InfoBoard controls
		_itemFront = GetNode<Sprite2D>("Control/Panel/InfoBoard/ItemFront");
		_amountToProduce = GetNode<Label>("Control/Panel/InfoBoard/AmountToProduce");
		_buttonMinusOne = GetNode<Button>("Control/Panel/InfoBoard/ButtonMinusOne");
		_buttonPlusOne = GetNode<Button>("Control/Panel/InfoBoard/ButtonPlusOne");
		_buttonSetOne = GetNode<Button>("Control/Panel/InfoBoard/ButtonSetOne");
		_buttonSet25Percent = GetNode<Button>("Control/Panel/InfoBoard/ButtonSet25Percent");
		_buttonSet50Percent = GetNode<Button>("Control/Panel/InfoBoard/ButtonSet50Percent");
		_buttonSetMax = GetNode<Button>("Control/Panel/InfoBoard/ButtonSetMax");
		_buttonProduce = GetNode<Button>("Control/Panel/InfoBoard/ButtonProduce");

		// Connect signals
		_closeButton.Pressed += OnCloseButtonPressed;
		_mithrilBarSelectButton.Pressed += OnMithrilBarSelectButtonPressed;
		_erithrydiumBarSelectButton.Pressed += OnErithrydiumBarSelectButtonPressed;

		_buttonMinusOne.Pressed += OnButtonMinusOnePressed;
		_buttonPlusOne.Pressed += OnButtonPlusOnePressed;
		_buttonSetOne.Pressed += OnButtonSetOnePressed;
		_buttonSet25Percent.Pressed += OnButtonSet25PercentPressed;
		_buttonSet50Percent.Pressed += OnButtonSet50PercentPressed;
		_buttonSetMax.Pressed += OnButtonSetMaxPressed;
		_buttonProduce.Pressed += OnButtonProducePressed;

		// Set Control/Panel to initially hidden
		var panel = GetNode<Node2D>("Control/Panel");
		if (panel != null)
		{
			panel.Visible = false;
		}

		if (_animationPlayer != null)
		{
			_animationPlayer.Play("RESET");
		}

		UpdateInfoBoard();
		GD.Print("Furnace3Menu: Ready completed");
	}

	public void SetFurnace(Furnace3 furnace)
	{
		_currentFurnace = furnace;
	}

	public void OpenMenu(Furnace3 furnace)
	{
		_currentFurnace = furnace;

		if (_animationPlayer != null && _animationPlayer.HasAnimation("Open"))
		{
			_animationPlayer.Play("Open");
		}

		UpdateInfoBoard();
		GD.Print("Furnace3Menu: Menu opened");
	}

	public void CloseMenu()
	{
		_currentFurnace = null;

		if (_animationPlayer != null && _animationPlayer.HasAnimation("Close"))
		{
			_animationPlayer.Play("Close");
		}

		GD.Print("Furnace3Menu: Menu closed");
	}

	private void OnCloseButtonPressed()
	{
		CloseMenu();
	}

	private void OnMithrilBarSelectButtonPressed()
	{
		_selectedResource = ResourceType.MithrilBar;
		_selectedAmount = 1;
		UpdateInfoBoard();
	}

	private void OnErithrydiumBarSelectButtonPressed()
	{
		_selectedResource = ResourceType.ErithrydiumBar;
		_selectedAmount = 1;
		UpdateInfoBoard();
	}

	private void OnButtonMinusOnePressed()
	{
		if (_selectedAmount > 0)
		{
			_selectedAmount--;
			UpdateInfoBoard();
		}
	}

	private void OnButtonPlusOnePressed()
	{
		if (_selectedAmount < 999)
		{
			_selectedAmount++;
			UpdateInfoBoard();
		}
	}

	private void OnButtonSetOnePressed()
	{
		_selectedAmount = 1;
		UpdateInfoBoard();
	}

	private void OnButtonSet25PercentPressed()
	{
		int maxAffordable = GetMaxAffordableAmount();
		_selectedAmount = Mathf.Max(1, maxAffordable / 4);
		UpdateInfoBoard();
	}

	private void OnButtonSet50PercentPressed()
	{
		int maxAffordable = GetMaxAffordableAmount();
		_selectedAmount = Mathf.Max(1, maxAffordable / 2);
		UpdateInfoBoard();
	}

	private void OnButtonSetMaxPressed()
	{
		_selectedAmount = GetMaxAffordableAmount();
		UpdateInfoBoard();
	}

	private void OnButtonProducePressed()
	{
		if (_currentFurnace == null || _selectedResource == ResourceType.None || _selectedAmount <= 0)
			return;

		if (!_currentFurnace.CanAffordRecipe(_selectedResource, _selectedAmount))
		{
			GD.Print("Furnace3Menu: Not enough resources to smelt!");
			return;
		}

		if (_currentFurnace.ConsumeRecipeResources(_selectedResource, _selectedAmount))
		{
			_currentFurnace.StartSmelting(_selectedResource, _selectedAmount);
			CloseMenu();
		}
	}

	private void UpdateInfoBoard()
	{
		if (_selectedResource == ResourceType.None)
		{
			if (_itemFront != null)
			{
				_itemFront.Texture = null;
			}
			if (_amountToProduce != null)
			{
				_amountToProduce.Text = "0";
			}
			if (_buttonProduce != null)
			{
				_buttonProduce.Modulate = new Color(1, 1, 1, 0.5f);
			}
			return;
		}

		// Update item sprite
		if (_itemFront != null)
		{
			var textureManager = TextureManager.Instance;
			if (textureManager != null)
			{
				_itemFront.Texture = textureManager.GetResourceTexture(_selectedResource);
			}
		}

		// Update amount label
		if (_amountToProduce != null)
		{
			_amountToProduce.Text = _selectedAmount.ToString();
		}

		// Update produce button state
		if (_buttonProduce != null)
		{
			bool canAfford = _currentFurnace?.CanAffordRecipe(_selectedResource, _selectedAmount) ?? false;
			_buttonProduce.Modulate = canAfford ? Colors.White : new Color(1, 1, 1, 0.5f);
		}
	}

	private int GetMaxAffordableAmount()
	{
		if (_selectedResource == ResourceType.None)
			return 0;

		var resourcesManager = ResourcesManager.Instance;
		if (resourcesManager == null)
			return 0;

		int maxAmount = 999;
		switch (_selectedResource)
		{
			case ResourceType.MithrilBar:
				int mithrilOre = GameSaveData.Instance.PlayerResources.GetResourceQuantity(ResourceType.MithrilOre);
				int wood = GameSaveData.Instance.PlayerResources.GetResourceQuantity(ResourceType.Wood);
				maxAmount = Mathf.Min(mithrilOre / 3, wood / 1);
				break;
			case ResourceType.ErithrydiumBar:
				int erithrydiumOre = GameSaveData.Instance.PlayerResources.GetResourceQuantity(ResourceType.ErithrydiumOre);
				int wood2 = GameSaveData.Instance.PlayerResources.GetResourceQuantity(ResourceType.Wood);
				maxAmount = Mathf.Min(erithrydiumOre / 3, wood2 / 1);
				break;
		}

		return Mathf.Max(0, maxAmount);
	}

}
