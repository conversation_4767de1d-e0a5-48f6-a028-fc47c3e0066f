using Godot;
using System.Collections.Generic;

public partial class CampfireMenu : CanvasLayer
{
	private AnimationPlayer _animationPlayer;
	private Button _closeButton;
	private Button _rabbitLegCookButton;
	
	// InfoBoard controls
	private Sprite2D _itemFront;
	private Label _amountToProduce;
	private Button _buttonMinusOne;
	private Button _buttonPlusOne;
	private Button _buttonSetOne;
	private Button _buttonSet25Percent;
	private Button _buttonSet50Percent;
	private Button _buttonSetMax;
	private Button _buttonProduce;

	private Campfire _campfire;
	private ResourceType _selectedResource = ResourceType.None;
	private int _selectedAmount = 1;

	private readonly Dictionary<ResourceType, Dictionary<ResourceType, int>> CookingRecipes = new()
	{
		{
			ResourceType.CookedRabbitLeg,
			new Dictionary<ResourceType, int>
			{
				{ ResourceType.RawRabbitLeg, 1 },
				{ ResourceType.Wood, 1 }
			}
		}
	};

	public override void _Ready()
	{
		_animationPlayer = GetNode<AnimationPlayer>("AnimationPlayer");
		_closeButton = GetNode<Button>("Control/Panel/CloseButton");
		_rabbitLegCookButton = GetNode<Button>("Control/Panel/ScrollContainer/VBoxContainer/ItemListWoodenPlank/Button");

		// InfoBoard controls
		_itemFront = GetNode<Sprite2D>("Control/Panel/InfoBoard/ItemFront");
		_amountToProduce = GetNode<Label>("Control/Panel/InfoBoard/AmountToProduce");
		_buttonMinusOne = GetNode<Button>("Control/Panel/InfoBoard/ButtonMinusOne");
		_buttonPlusOne = GetNode<Button>("Control/Panel/InfoBoard/ButtonPlusOne");
		_buttonSetOne = GetNode<Button>("Control/Panel/InfoBoard/ButtonSetOne");
		_buttonSet25Percent = GetNode<Button>("Control/Panel/InfoBoard/ButtonSet25Percent");
		_buttonSet50Percent = GetNode<Button>("Control/Panel/InfoBoard/ButtonSet50Percent");
		_buttonSetMax = GetNode<Button>("Control/Panel/InfoBoard/ButtonSetMax");
		_buttonProduce = GetNode<Button>("Control/Panel/InfoBoard/ButtonProduce");

		// Connect signals
		_closeButton.Pressed += OnCloseButtonPressed;
		_rabbitLegCookButton.Pressed += OnRabbitLegCookButtonPressed;
		
		_buttonMinusOne.Pressed += OnButtonMinusOnePressed;
		_buttonPlusOne.Pressed += OnButtonPlusOnePressed;
		_buttonSetOne.Pressed += OnButtonSetOnePressed;
		_buttonSet25Percent.Pressed += OnButtonSet25PercentPressed;
		_buttonSet50Percent.Pressed += OnButtonSet50PercentPressed;
		_buttonSetMax.Pressed += OnButtonSetMaxPressed;
		_buttonProduce.Pressed += OnButtonProducePressed;

		if (_animationPlayer != null)
		{
			_animationPlayer.Play("RESET");
		}

		UpdateInfoBoard();
	}

	public void SetCampfire(Campfire campfire)
	{
		_campfire = campfire;
	}

	public void OpenMenu()
	{
		CommonSignals.Instance?.EmitPlayerMovementEnabled(false);

		if (_animationPlayer != null)
		{
			_animationPlayer.Play("Open");
		}

		UpdateInfoBoard();
	}

	private void CloseMenu()
	{
		CommonSignals.Instance?.EmitPlayerMovementEnabled(true);

		if (_animationPlayer != null)
		{
			_animationPlayer.Play("Close");
		}
	}

	private void OnCloseButtonPressed()
	{
		CloseMenu();
	}

	private void OnRabbitLegCookButtonPressed()
	{
		_selectedResource = ResourceType.CookedRabbitLeg;
		_selectedAmount = 1;
		UpdateInfoBoard();
	}

	private void OnButtonMinusOnePressed()
	{
		if (_selectedAmount > 0)
		{
			_selectedAmount--;
			UpdateInfoBoard();
		}
	}

	private void OnButtonPlusOnePressed()
	{
		if (_selectedAmount < 999)
		{
			_selectedAmount++;
			UpdateInfoBoard();
		}
	}

	private void OnButtonSetOnePressed()
	{
		_selectedAmount = 1;
		UpdateInfoBoard();
	}

	private void OnButtonSet25PercentPressed()
	{
		int maxAffordable = GetMaxAffordableAmount();
		_selectedAmount = Mathf.Max(1, maxAffordable / 4);
		UpdateInfoBoard();
	}

	private void OnButtonSet50PercentPressed()
	{
		int maxAffordable = GetMaxAffordableAmount();
		_selectedAmount = Mathf.Max(1, maxAffordable / 2);
		UpdateInfoBoard();
	}

	private void OnButtonSetMaxPressed()
	{
		_selectedAmount = GetMaxAffordableAmount();
		UpdateInfoBoard();
	}

	private void OnButtonProducePressed()
	{
		if (_campfire == null || _selectedResource == ResourceType.None || _selectedAmount <= 0)
			return;

		if (!_campfire.CanAffordRecipe(_selectedResource, _selectedAmount))
		{
			GD.Print("CampfireMenu: Not enough resources to cook!");
			return;
		}

		if (_campfire.ConsumeRecipeResources(_selectedResource, _selectedAmount))
		{
			_campfire.StartCooking(_selectedResource, _selectedAmount);
			CloseMenu();
		}
	}

	private void UpdateInfoBoard()
	{
		if (_selectedResource == ResourceType.None)
		{
			if (_itemFront != null)
			{
				_itemFront.Texture = null;
			}
			if (_amountToProduce != null)
			{
				_amountToProduce.Text = "0";
			}
			if (_buttonProduce != null)
			{
				_buttonProduce.Modulate = new Color(1, 1, 1, 0.5f);
			}
			return;
		}

		// Update item sprite
		if (_itemFront != null)
		{
			var textureManager = TextureManager.Instance;
			if (textureManager != null)
			{
				_itemFront.Texture = textureManager.GetResourceTexture(_selectedResource);
			}
		}

		// Update amount label
		if (_amountToProduce != null)
		{
			_amountToProduce.Text = _selectedAmount.ToString();
		}

		// Update produce button state
		if (_buttonProduce != null)
		{
			bool canAfford = _campfire?.CanAffordRecipe(_selectedResource, _selectedAmount) ?? false;
			_buttonProduce.Modulate = canAfford ? Colors.White : new Color(1, 1, 1, 0.5f);
		}
	}

	private int GetMaxAffordableAmount()
	{
		if (_selectedResource == ResourceType.None || _campfire == null)
			return 0;

		if (!CookingRecipes.TryGetValue(_selectedResource, out var recipe))
			return 0;

		var resourcesManager = ResourcesManager.Instance;
		if (resourcesManager == null)
			return 0;

		int maxAmount = 999;
		foreach (var ingredient in recipe)
		{
			int available = GameSaveData.Instance.PlayerResources.GetResourceQuantity(ingredient.Key);
			int possibleAmount = available / ingredient.Value;
			maxAmount = Mathf.Min(maxAmount, possibleAmount);
		}

		return Mathf.Max(0, maxAmount);
	}
}
