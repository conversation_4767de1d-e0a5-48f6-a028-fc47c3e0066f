[gd_scene load_steps=11 format=3 uid="uid://bsnbmuj68c6tw"]

[ext_resource type="Texture2D" uid="uid://cqiw7fui1lnrg" path="res://resources/solaria/buildings/animated/Forge1.png" id="1_fe2r0"]
[ext_resource type="Script" uid="uid://bd0uiwph65cma" path="res://scenes/mapObjects/buildings/Furnace2.cs" id="1_script"]
[ext_resource type="PackedScene" uid="uid://otpfc634hhga" path="res://scenes/UI/progress/ProgressBar.tscn" id="3_dvujk"]
[ext_resource type="PackedScene" uid="uid://cl5piewhmq3np" path="res://scenes/UI/buildingMenus/Furnace1Menu.tscn" id="3_gptgy"]
[ext_resource type="PackedScene" uid="uid://b8xf7h2lam3pq" path="res://scenes/UI/progress/ProgressBarVertical.tscn" id="5_kcax7"]

[sub_resource type="Animation" id="Animation_27ba4"]
resource_name = "AnimateFurnace"
length = 0.4
loop_mode = 1
step = 0.1
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Furnace:frame")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.1, 0.2, 0.3),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 1,
"values": [1, 2, 3, 4]
}

[sub_resource type="Animation" id="Animation_t586u"]
length = 0.001
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Furnace:frame")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [0]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_u2oqx"]
_data = {
&"AnimateFurnace": SubResource("Animation_27ba4"),
&"RESET": SubResource("Animation_t586u")
}

[sub_resource type="RectangleShape2D" id="RectangleShape2D_usjcl"]
size = Vector2(28, 23.5)

[sub_resource type="CircleShape2D" id="CircleShape2D_gh3ed"]
radius = 16.0

[node name="Furnace2" type="Node2D"]
y_sort_enabled = true
script = ExtResource("1_script")

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
libraries = {
&"": SubResource("AnimationLibrary_u2oqx")
}
speed_scale = 0.6

[node name="Furnace" type="Sprite2D" parent="."]
y_sort_enabled = true
texture = ExtResource("1_fe2r0")
hframes = 5

[node name="CraftingResource" type="Sprite2D" parent="."]
y_sort_enabled = true
position = Vector2(0, -2)
scale = Vector2(0.6, 0.75)
offset = Vector2(0, -30)

[node name="StaticBody2D" type="StaticBody2D" parent="."]

[node name="CollisionShape2D" type="CollisionShape2D" parent="StaticBody2D"]
position = Vector2(0, 17.25)
shape = SubResource("RectangleShape2D_usjcl")

[node name="ProgressBar" parent="." instance=ExtResource("3_dvujk")]
position = Vector2(1.42109e-14, 30)
scale = Vector2(1.75, 0.473)

[node name="PlayerDetector" type="Area2D" parent="."]

[node name="CollisionShape2D" type="CollisionShape2D" parent="PlayerDetector"]
position = Vector2(0, 18)
shape = SubResource("CircleShape2D_gh3ed")

[node name="Furnace2Menu" parent="." instance=ExtResource("3_gptgy")]

[node name="ProgressBarVertical" parent="." instance=ExtResource("5_kcax7")]
visible = false
z_index = 1
position = Vector2(14, 11)
